

import '../imports/core_imports.dart';

class ScreenSizer {

  int calculateCrossAxisCount(double width) {
    if (width >= 2560) return 5;
    if (width >= 1920) return 5;
    if (width >= 1600) return 5;
    if (width >= 1400) return 5;
    if (width >= 1000) return 3;
    return 2;
  }


  // ... (keep all your existing helper methods below) ...
  double calculateShoppingSize(double screenWidth, double screenHeight) {
    final aspectRatio = screenWidth / screenHeight;
    print(
        "Screen width: $screenWidth, height: $screenHeight, ratio: $aspectRatio");

    if (screenWidth == 1920) {
      if (screenHeight >= 1060) {
        return 1.04;
      } else {
        return 1.12;
      }
    }

    if (aspectRatio >= 2.0) {
      return 1.2;
    } else if (aspectRatio >= 1.8) {
      return 1.21;
    } else if (aspectRatio >= 1.7) {
      return 1.03;
    } else if (aspectRatio >= 1.4) {
      return 1.02;
    } else if (aspectRatio >= 1.2) {
      return 0.8;
    } else {
      return 0.75;
    }
  }



  double calculateChildAspectRatio(double screenWidth, double screenHeight) {
    final aspectRatio = screenWidth / screenHeight;

    if (screenWidth >= 1920) {
      return screenHeight >= 1060 ? 1.0 : 1.1;
    }

    if (aspectRatio >= 2.0) return 1.3; // Wider screens (e.g., foldables)
    if (aspectRatio >= 1.8) return 1.2; // Wide phones
    if (aspectRatio >= 1.6) return 1.1; // Standard phones
    if (aspectRatio >= 1.4) return 1.0; // Narrower phones
    if (aspectRatio >= 1.2) return 0.9; // Tablets or square-ish screens
    return 0.8; // Default for very narrow screens
  }

  // double calculateChildAspectRatio(double screenWidth, double screenHeight) {
  //   final aspectRatio = screenWidth / screenHeight;
  //
  //   if (screenWidth >= 1920) {
  //     return screenHeight >= 1060 ? 1.04 : 1.12;
  //   }
  //
  //   if (aspectRatio >= 2.0) return 1.2;
  //   if (aspectRatio >= 1.8) return 1.21;
  //   if (aspectRatio >= 1.7) return 1.03;
  //   if (aspectRatio >= 1.4) return 1.02;
  //   if (aspectRatio >= 1.2) return 0.8;
  //   return 0.75;
  // }

  int calculateCrossAxisCountScreenSize({required BuildContext context, required Size screenSize}) {
    final w = screenSize.width; // Use logical pixels, not physical
    if (w >= 1200) return 5; // Large tablets or desktops
    if (w >= 800) return 4; // Medium tablets
    if (w >= 600) return 3; // Small tablets or large phones
    return 2; // Phones
  } 

  // int calculateCrossAxisCountScreenSize({required BuildContext context,required Size screenSize}) {
  //   final w = screenSize.width * MediaQuery.of(context).devicePixelRatio;
  //   if (w >= 2560) return 4;
  //   if (w >= 1920) return 4;
  //   if (w >= 1400) return 4;
  //   return 4;
  // }

  double calculateImageHeight(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final aspectRatio = size.width / size.height;

    // Specifically handle 1920px width
    if (size.width == 1920) {
      if (size.width >= 1060) {
        return 240; // Works for 1920x1080 and similar
      } else {
        return 220; // Safer ratio for shorter height like 1006
      }
    }

    if (aspectRatio >= 2.0) {
      // Ultra-wide screens (e.g., 3440×1440, 3840×1600)
      return 240;
    } else if (aspectRatio >= 1.8) {
      // 16:9 and wider (e.g., 1920×1080, 2560×1440)
      return 200;
    } else if (aspectRatio >= 1.7) {
      return 200; // Covers 1920x1080
    } else if (aspectRatio >= 1.4) {
      // Retina/HiDPI screens (e.g., 2880×1800, 3584×2240)
      return 200;
    } else if (aspectRatio >= 1.2) {
      // Taller screens
      return 200;
    } else {
      // Very tall or square-ish screens
      return 200;
    }
  }

double calculateChildDetailScrenAspectRatio(double screenWidth, double screenHeight) {
  final aspectRatio = screenWidth / screenHeight;
  print("Screen width: $screenWidth, height: $screenHeight, ratio: $aspectRatio");

  // Specifically handle 1920px width
  if (screenWidth == 1920) {
    if (screenHeight >= 1060) {
      //mac 1920* 1080
      return 0.78 ; // Works for 1920x1080 and similar
    } else {
      //linux 1920*1006
      return 0.93; // Safer ratio for shorter height like 1006
    }
  }

  if (aspectRatio >= 2.0) {
    // Ultra-wide screens (e.g., 3440×1440, 3840×1600)
    return 1.2;
  } else if (aspectRatio >= 1.8) {
    //windos 1920*1080
    // 16:9 and wider (e.g., 1920×1080, 2560×1440)
    //and even 1006
    return 0.95;
    //1920 1006
  } else if (aspectRatio >= 1.7) {
    return 0.85; // Covers 1920x1080
  }
  else if (aspectRatio >= 1.4) {
    // Retina/HiDPI screens (e.g., 2880×1800, 3584×2240)
    return 0.75;
  } else if (aspectRatio >= 1.2) {
    // Taller screens
    return 0.8;
  } else {
    // Very tall or square-ish screens
    return 0.75;
  }
}

  double calculateRecipeImageHeight(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final aspectRatio = size.width / size.height;

    // Specifically handle 1920px width
    if (size.width == 1920) {
      if (size.width >= 1060) {
        return 260; // Works for 1920x1080 and similar
      } else {
        return 220; // Safer ratio for shorter height like 1006
      }
    }

    if (aspectRatio >= 2.0) {
      // Ultra-wide screens (e.g., 3440×1440, 3840×1600)
      return 240;
    } else if (aspectRatio >= 1.8) {
      // 16:9 and wider (e.g., 1920×1080, 2560×1440)
      //and even 1006
      return 200;
      //1920 1006
    } else if (aspectRatio >= 1.7) {
      return 200; // Covers 1920x1080
    } else if (aspectRatio >= 1.4) {
      // Retina/HiDPI screens (e.g., 2880×1800, 3584×2240)
      return 230;
    } else if (aspectRatio >= 1.2) {
      // Taller screens
      return 200;
    } else {
      // Very tall or square-ish screens
      return 200;
    }
  }

  double calculateRecipeCardHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return screenHeight * 0.4; // Adjust based on your design (e.g., 400.h)
  }

  // double calculateRecipeImageHeight(BuildContext context) {
  //   final screenHeight = MediaQuery.of(context).size.height;
  //   return screenHeight * 0.25; // Adjust as needed
  // }


  double getVisibleScreenHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final appBarHeight = kToolbarHeight; // Default AppBar height
    final statusBarHeight = mediaQuery.padding.top;
    return mediaQuery.size.height - statusBarHeight - appBarHeight;
  }

}