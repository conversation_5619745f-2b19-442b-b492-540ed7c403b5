import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class UserStorageService {
  static const String _userKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _isOnlineKey = 'is_online';
  static const String _licenseExpiredKey = 'license_expired';

  // User data model
  static Future<void> saveUserData({
    required String name,
    required String email,
    required String phone,
    String? profileImage,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final userData = {
      'name': name,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
    };
    await prefs.setString(_userKey, jsonEncode(userData));
  }

  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(_userKey);
    if (userDataString != null) {
      return jsonDecode(userDataString);
    }
    return null;
  }

  // Login status
  static Future<void> setLoggedIn(bool isLoggedIn) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, isLoggedIn);
  }

  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  // Online status
  static Future<void> setOnlineStatus(bool isOnline) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isOnlineKey, isOnline);
  }

  static Future<bool> getOnlineStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isOnlineKey) ?? false;
  }

  // License status
  static Future<void> setLicenseExpired(bool isExpired) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_licenseExpiredKey, isExpired);
  }

  static Future<bool> isLicenseExpired() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_licenseExpiredKey) ?? false;
  }

  // Clear all data (logout)
  static Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.setBool(_isLoggedInKey, false);
  }

  // Get user name
  static Future<String> getUserName() async {
    final userData = await getUserData();
    return userData?['name'] ?? 'User';
  }

  // Get user email
  static Future<String> getUserEmail() async {
    final userData = await getUserData();
    return userData?['email'] ?? '';
  }
}
