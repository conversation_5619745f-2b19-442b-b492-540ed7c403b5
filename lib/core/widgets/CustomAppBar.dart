import 'package:towchain_service_provider/core/widgets/custom_text.dart';
import '../imports/core_imports.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? leading;
  final List<Widget>? actions;
  final bool centerTitle;

  const CustomAppBar({
    super.key,
    required this.title,
    this.leading,
    this.actions,
    this.centerTitle = true,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: CustomText(
        text: title,
        fontFamily: 'Urbanist-SemiBold',
        size: 18,
        weight: FontWeight.w600,
        color: AppColors.whiteColor,
      ),
      centerTitle: centerTitle,
      titleSpacing: 0,
      leading: leading,
      actions: actions,
      backgroundColor: AppColors.primaryGreyColor, // Dark background color
      elevation: 0, // No shadow
    );
  }
}
