import 'package:flutter/material.dart';

import '../theme/colors.dart';
import 'custom_text.dart';

class CustomDropDownMobile<T> extends StatelessWidget {
  final String? label; // Made the label optional
  final String? value;
  final List<T> options;
  final ValueChanged<String?> onChanged;
  final String Function(T) getDisplayString;

  const CustomDropDownMobile({
    super.key,
    this.label, // The label is now optional
    required this.value,
    required this.options,
    required this.onChanged,
    required this.getDisplayString,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return PopupMenuButton<String>(
          onSelected: onChanged,
          itemBuilder: (context) {
            return options.map((option) {
              final displayString = getDisplayString(option);
              return PopupMenuItem<String>(
                value: displayString,
                child: CustomText(
                  text: displayString,
                  size: 12,
                  weight: displayString == value
                      ? FontWeight.w600
                      : FontWeight.w400,
                  color: displayString == value
                      ? AppColors.primaryColor
                      : AppColors.primaryGreyColor,
                ),
              );
            }).toList();
          },
          color: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          // Set the constraints to force full width
          constraints: BoxConstraints(minWidth: constraints.maxWidth),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.lightestGreyColor),
              borderRadius: BorderRadius.circular(8.0),
              color: AppColors.whiteColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  text: value ?? 'Select',
                  color: AppColors.primaryGreyColor,
                  weight: FontWeight.w400,
                  size: 12,
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    size: 18,
                    color: AppColors.blackColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
