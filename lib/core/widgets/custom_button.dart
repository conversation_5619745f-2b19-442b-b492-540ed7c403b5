

import 'package:towchain_service_provider/core/extensions/context_extensions.dart';

import '../imports/core_imports.dart';
import 'custom_text.dart';
import 'loading_indicator.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final Color? color;
  final IconData? icon;
  final Color? iconColor;
  final Color? textColor;
  final VoidCallback onPressed;
  final double? width;
  final double? height;
  final double? fontSize;
  final double? iconSize;
  final double? borderRadius;
  final bool? hasBorder;
  final bool? isLoading;
  final bool? isDisabled;
  final bool? isUnderline;
  final String fontFamily;
  final bool? showTrailingArrow;
  final FontWeight? weight;

  const CustomButton({
    super.key,
    required this.text,
    this.color,
    this.icon,
    this.iconColor,
    this.textColor,
    required this.onPressed,
    this.width,
    this.height,
    this.weight = FontWeight.w400,
    this.fontSize,
    this.iconSize,
    this.borderRadius,
    this.hasBorder,
    this.isLoading,
    this.isDisabled,
    this.isUnderline = false,
    this.fontFamily = 'Urbanist',
    this.showTrailingArrow = false,
  });

  @override
  Widget build(BuildContext context) {
    // Determine shadow color based on button color
    final effectiveShadowColor = color == Colors.white
        ? Colors.grey.withValues(alpha: 0.3) // Subtle shadow for white buttons
        : color?.withValues(alpha: 0.5) ?? context.theme.shadowColor;

    // Ensure border color contrasts with white background
    final effectiveBorderColor = color == Colors.white
        ? Colors.grey.shade200 // Slightly darker border for white buttons
        : color ?? context.theme.iconTheme.color!;

    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: hasBorder == true
            ? context.theme.scaffoldBackgroundColor
            : color ?? context.theme.primaryColor,
        shape: hasBorder == true
            ? RoundedRectangleBorder(
          side: BorderSide(
            color: effectiveBorderColor,
            width: 1.5, // Slightly thicker border for visibility
          ),
          borderRadius: BorderRadius.circular(
            borderRadius ?? 20,
          ),
        )
            : RoundedRectangleBorder(
          side: color == Colors.white
              ? BorderSide(
            color: Colors
                .grey.shade200, // Default border for white buttons
            width: 1.5,
          )
              : BorderSide
              .none, // No border for non-white buttons unless specified
          borderRadius: BorderRadius.circular(
            borderRadius ?? 18,
          ),
        ),
        fixedSize: Size(
          width ?? context.width,
          height ?? 70,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 0.0,
        ),
        elevation: 4, // Non-zero elevation for shadow
        shadowColor: effectiveShadowColor, // Dynamic shadow color
      ),
      onPressed: isDisabled == true || isLoading == true ? () {} : onPressed,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: isLoading == true
            ? const LoadingIndicator(
          key: ValueKey('loading'),
          size: 14,
          strokeWidth: 3,
          color: Colors.white,
        )
            : Row(
          key: const ValueKey('content'),
          mainAxisAlignment: showTrailingArrow == true
              ? MainAxisAlignment.spaceBetween
              : MainAxisAlignment.center,
          children: [
            if (icon != null)
              Icon(
                icon,
                color: iconColor ?? Colors.white,
                size: iconSize ?? 24,
              ),
            if (icon != null) SizedBox(width: 15),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: showTrailingArrow == true ? 16.0 : 0.0,
                ),
                child: CustomText(
                  text: text,
                  color: textColor ?? AppColors.whiteColor,
                  size: fontSize,
                  weight: weight,
                  decoration: isUnderline == true
                      ? TextDecoration.underline
                      : TextDecoration.none,
                  align: showTrailingArrow == true
                      ? TextAlign.left
                      : TextAlign.center,
                ),
              ),
            ),
            if (showTrailingArrow == true)
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: iconColor ?? textColor ?? AppColors.whiteColor,
                  size: iconSize ?? 16,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
