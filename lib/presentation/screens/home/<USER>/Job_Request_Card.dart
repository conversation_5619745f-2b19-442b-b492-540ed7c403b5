import 'package:towchain_service_provider/core/assets_manager.dart';
import 'package:towchain_service_provider/core/imports/core_imports.dart';
import 'package:towchain_service_provider/core/widgets/custom_button.dart';
import 'package:towchain_service_provider/core/widgets/custom_text.dart';
import 'package:towchain_service_provider/presentation/screens/jobRequest/model/JobRequestModel.dart';

class JobRequestCard extends StatelessWidget {
  final JobRequest job;
  final VoidCallback onDetailsPressed;
  final bool showPhone;
  final String btnTitle;
  final VoidCallback? onCallPressed;

  const JobRequestCard({
    super.key,
    required this.job,
    required this.onDetailsPressed,
    this.showPhone = false,
    this.btnTitle = 'Details',
    this.onCallPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer info row
            Row(
              children: [
                // Customer avatar
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.lightestGreyColor,
                      width: 1,
                    ),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      job.customerImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppColors.lightestGreyColor,
                          child: const Icon(
                            Icons.person,
                            color: AppColors.textGreyColor,
                            size: 20,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Customer name
                Expanded(
                  child: CustomText(
                    text: job.customerName,
                    size: 20,
                    weight: FontWeight.w600,
                    color: AppColors.blackColor,
                    fontFamily: FontsManager.urbanist,
                  ),
                ),
                // Call button or Arrow icon
                if (showPhone && onCallPressed != null)
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.primaryColor,
                    ),
                    child: IconButton(
                      onPressed: onCallPressed,
                      icon: const Icon(
                        Icons.call,
                        color: AppColors.blackColor,
                        size: 25,
                      ),
                    ),
                  )
                else
                  const Icon(
                    Icons.chevron_right,
                    color: AppColors.primaryGreyColor,
                    size: 25,
                  ),
              ],
            ),
            const SizedBox(height: 10),

            // Pickup location
            _buildLocationRow(
              label: 'PICK UP',
              location: job.pickupLocation,
              isPickup: true,
            ),
            const SizedBox(height: 12),

            // Dropoff location
            _buildLocationRow(
              label: 'DROP OFF',
              location: job.dropoffLocation,
              isPickup: false,
            ),
            const SizedBox(height: 13),

            // Job details row
            Row(
              children: [
                // Duration
                const SizedBox(width: 5),
                _buildDetailItem(icon: AssetsManager.time, text: job.duration),
                const SizedBox(width: 24),

                // Distance
                _buildDetailItem(
                  icon: AssetsManager.navigateGray,
                  text: job.distance,
                ),
                const SizedBox(width: 24),

                // Service type
                _buildDetailItem(
                  icon: AssetsManager.repair,
                  text: job.serviceType,
                ),
              ],
            ),

            const SizedBox(height: 15),
            Divider(height: 0.2, color: AppColors.lightestGreyColor),
            const SizedBox(height: 10),
            // Details button
            CustomButton(
              text: 'Details',
              height: 55,
              fontSize: 16,
              borderRadius: 14,
              fontFamily: FontsManager.urbanist,
              weight: FontWeight.w600,
              color: AppColors.primaryGreyColor,
              textColor: AppColors.primaryColor,
              onPressed: onDetailsPressed,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationRow({
    required String label,
    required String location,
    required bool isPickup,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location dot
        Container(width: 8, height: 8, margin: const EdgeInsets.only(top: 0)),
        const SizedBox(width: 0),

        // Location details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: label,
                size: 12,
                weight: FontWeight.w500,
                color: AppColors.lightGrayText,
              ),
              const SizedBox(height: 0),
              CustomText(
                text: location,
                size: 14,
                weight: FontWeight.w400,
                fontFamily: FontsManager.urbanist,
                color: AppColors.blackColor,
                maxLine: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem({required String icon, required String text}) {
    return Row(
      children: [
        Image.asset(icon, width: 16, height: 16),
        const SizedBox(width: 6),
        CustomText(
          text: text,
          size: 15,
          weight: FontWeight.w600,
          fontFamily: FontsManager.urbanist,
          color: AppColors.primaryGreyColor,
        ),
      ],
    );
  }
}
