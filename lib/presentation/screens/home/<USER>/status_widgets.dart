import 'package:flutter/material.dart';
import 'package:towchain_service_provider/core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/widgets/custom_text.dart';

class OnlineStatusWidget extends StatelessWidget {
  final bool isOnline;

  const OnlineStatusWidget({super.key, required this.isOnline});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 10),
      decoration: BoxDecoration(
        color: isOnline ? AppColors.green : AppColors.orange,
      ),
      child: Row(
        children: [
          Container(
            child: Image.asset(
              height: 50,
              width: 50,
              isOnline ? AssetsManager.offlineIcon : AssetsManager.offlineIcon,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: isOnline ? "You are Online!" : "You are Offline!",
                  color: Colors.white,
                  size: 16,
                  weight: FontWeight.w600,
                ),
                CustomText(
                  text: isOnline
                      ? "You can now accept jobs"
                      : "Go online to start accepting jobs",
                  color: Colors.white.withValues(alpha: 0.9),
                  size: 12,
                  weight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LicenseExpiredWidget extends StatelessWidget {
  final VoidCallback onRenewPressed;

  const LicenseExpiredWidget({super.key, required this.onRenewPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 55,
      padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 8),
      decoration: BoxDecoration(color: AppColors.red),
      child: Row(
        children: [
          Container(
            child: Image.asset(
              height: 50,
              width: 50,
              AssetsManager.licenseExpired,
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: "Driving License Expired",
                  color: Colors.white,
                  size: 14,
                  weight: FontWeight.w600,
                ),
                const SizedBox(height: 2),
                CustomText(
                  text:
                      "Your license is no longer valid. Upload a valid one to proceed.",
                  color: Colors.white.withValues(alpha: 0.9),
                  size: 10,
                  weight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
