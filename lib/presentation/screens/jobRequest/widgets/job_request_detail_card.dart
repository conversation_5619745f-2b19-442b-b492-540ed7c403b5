import 'package:towchain_service_provider/core/assets_manager.dart';
import 'package:towchain_service_provider/core/imports/core_imports.dart';
import 'package:towchain_service_provider/presentation/screens/jobRequest/model/JobRequestModel.dart';

class JobRequestDetailCard extends StatelessWidget {
  final JobRequest job;
  final VoidCallback? onOfferChargesPressed;
  final VoidCallback? onDeclinePressed;

  const JobRequestDetailCard({
    super.key,
    required this.job,
    this.onOfferChargesPressed,
    this.onDeclinePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer info row
            Row(
              children: [
                // Customer avatar
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.lightestGreyColor,
                      width: 1,
                    ),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      job.customerImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppColors.lightestGreyColor,
                          child: const Icon(
                            Icons.person,
                            color: AppColors.textGreyColor,
                            size: 25,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Customer name
                Expanded(
                  child: Text(
                    job.customerName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.blackColor,
                      fontFamily: FontsManager.urbanist,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Pickup location
            _buildLocationRow(
              label: 'PICK UP',
              location: job.pickupLocation,
              isPickup: true,
            ),
            const SizedBox(height: 12),

            // Dropoff location
            _buildLocationRow(
              label: 'DROP OFF',
              location: job.dropoffLocation,
              isPickup: false,
            ),
            const SizedBox(height: 16),

            // Job details row
            Row(
              children: [
                _buildDetailItem(
                  icon: AssetsManager.time,
                  text: job.duration,
                ),
                const SizedBox(width: 24),
                _buildDetailItem(
                  icon: AssetsManager.navigateGray,
                  text: job.distance,
                ),
                const SizedBox(width: 24),
                _buildDetailItem(
                  icon: AssetsManager.repair,
                  text: job.serviceType,
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Action buttons
            Row(
              children: [
                // Offer Charges button
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextButton(
                      onPressed: onOfferChargesPressed,
                      child: const Text(
                        'Offer Charges',
                        style: TextStyle(
                          color: AppColors.blackColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: FontsManager.urbanist,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Decline button
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppColors.blackColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextButton(
                      onPressed: onDeclinePressed,
                      child: const Text(
                        'Decline',
                        style: TextStyle(
                          color: AppColors.whiteColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: FontsManager.urbanist,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationRow({
    required String label,
    required String location,
    required bool isPickup,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location dot
        Container(
          width: 8,
          height: 8,
          margin: const EdgeInsets.only(top: 6),
          decoration: BoxDecoration(
            color: isPickup ? Colors.green : Colors.red,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),

        // Location details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.lightGrayText,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                location,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontsManager.urbanist,
                  color: AppColors.blackColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem({required String icon, required String text}) {
    return Row(
      children: [
        Image.asset(icon, width: 16, height: 16),
        const SizedBox(width: 6),
        Text(
          text,
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            fontFamily: FontsManager.urbanist,
            color: AppColors.primaryGreyColor,
          ),
        ),
      ],
    );
  }
}
