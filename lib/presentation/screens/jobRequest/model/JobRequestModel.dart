class JobRequest {
  final String id;
  final String customerName;
  final String customerImage;
  final String pickupLocation;
  final String dropoffLocation;
  final String duration;
  final String distance;
  final String serviceType;

  JobRequest({
    required this.id,
    required this.customerName,
    required this.customerImage,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.duration,
    required this.distance,
    required this.serviceType,
  });
}
