import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:towchain_service_provider/core/widgets/CustomAppBar.dart';
import 'package:towchain_service_provider/presentation/screens/home/<USER>/status_widgets.dart';
import 'package:towchain_service_provider/presentation/screens/jobRequest/model/JobRequestModel.dart';
import '../../../../core/imports/core_imports.dart';
import '../../../../core/assets_manager.dart';

class JobsRequestDetailScreen extends StatefulWidget {
  const JobsRequestDetailScreen({super.key});

  @override
  State<JobsRequestDetailScreen> createState() =>
      _JobsRequestDetailScreenState();
}

class _JobsRequestDetailScreenState extends State<JobsRequestDetailScreen> {
  late GoogleMapController mapController;
  final LatLng _center = const LatLng(
    30.7046,
    76.7179,
  ); // Use a single, consistent location
  final Set<Marker> _markers = {};
  BitmapDescriptor? customMarkerIcon;

  // Status variables
  final bool _isOnline = false;
  final int _notificationCount = 1;
  final jobRequest = JobRequest(
    id: '1',
    customerName: 'David Morel',
    customerImage: AssetsManager.person, // Using placeholder
    pickupLocation: 'Westheimer Rd Santa Ana, Illinois 85486',
    dropoffLocation: 'Thornridge Cir at 1901 Shiloh, Hawaii 81063',
    duration: '15min',
    distance: '2.5km',
    serviceType: 'Tire Repairs',
  );
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightestGreyColor,
      appBar: CustomAppBar(
        title: 'View Details',
        centerTitle: false,
        leading: IconButton(
          icon: Image.asset(AssetsManager.arrowLeft, width: 24, height: 24),
          onPressed: () {
            context.pop();
          },
        ),
      ),
      body: Column(
        children: [
          // Status widgets container - only show when needed
          Container(
            padding: const EdgeInsets.all(0),
            child: Column(
              children: [
                // Online/Offline status widget - always show
                OnlineStatusWidget(isOnline: _isOnline),
              ],
            ),
          ),
          // Map View (wrapped in Expanded)
          Expanded(
            child: Stack(
              children: [
                GoogleMap(
                  onMapCreated: _onMapCreated,
                  initialCameraPosition: CameraPosition(
                    target: _center, // Use the correct center
                    zoom: 11.0,
                  ),
                  markers: _markers,
                  myLocationButtonEnabled: false,
                ),
                Positioned(
                  right: 15.0,
                  top: 100,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.0),
                        border: Border.all(
                          color: AppColors.hintColor,
                          width: 1.0,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(3.0),
                        child: Column(
                          children: [
                            Stack(
                              children: [
                                FloatingActionButton(
                                  heroTag: "fab1",
                                  mini: true,
                                  onPressed: () {
                                    context.push('/jobsRequest');
                                  },
                                  backgroundColor: Colors.transparent,
                                  shape: const CircleBorder(),
                                  child: Image.asset(
                                    height: 40,
                                    width: 40,
                                    AssetsManager.yellowTow,
                                  ),
                                ),
                                // Notification badge - only show when count > 0
                                if (_notificationCount > 0)
                                  Positioned(
                                    right: 4,
                                    top: 2,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.green,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 0,
                                        ),
                                      ),
                                      constraints: const BoxConstraints(
                                        minWidth: 20,
                                        minHeight: 20,
                                      ),
                                      child: Text(
                                        _notificationCount.toString(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab2",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 40,
                                width: 40,
                                AssetsManager.mapNearby,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab3",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 40,
                                width: 40,
                                AssetsManager.navigate,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab4",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 20,
                                width: 20,
                                AssetsManager.mapMini,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab5",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 25,
                                width: 25,
                                AssetsManager.plus,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab6",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 25,
                                width: 25,
                                AssetsManager.minus,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Job Request Card positioned at bottom
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Customer info row
                          Row(
                            children: [
                              // Customer avatar
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: AppColors.lightestGreyColor,
                                    width: 1,
                                  ),
                                ),
                                child: ClipOval(
                                  child: Image.asset(
                                    jobRequest.customerImage,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        color: AppColors.lightestGreyColor,
                                        child: const Icon(
                                          Icons.person,
                                          color: AppColors.textGreyColor,
                                          size: 25,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              // Customer name
                              Expanded(
                                child: Text(
                                  jobRequest.customerName,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.blackColor,
                                    fontFamily: FontsManager.urbanist,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Pickup location
                          _buildLocationRow(
                            label: 'PICK UP',
                            location: jobRequest.pickupLocation,
                            isPickup: true,
                          ),
                          const SizedBox(height: 12),

                          // Dropoff location
                          _buildLocationRow(
                            label: 'DROP OFF',
                            location: jobRequest.dropoffLocation,
                            isPickup: false,
                          ),
                          const SizedBox(height: 16),

                          // Job details row
                          Row(
                            children: [
                              _buildDetailItem(
                                icon: AssetsManager.time,
                                text: jobRequest.duration,
                              ),
                              const SizedBox(width: 24),
                              _buildDetailItem(
                                icon: AssetsManager.navigateGray,
                                text: jobRequest.distance,
                              ),
                              const SizedBox(width: 24),
                              _buildDetailItem(
                                icon: AssetsManager.repair,
                                text: jobRequest.serviceType,
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          // Action buttons
                          Row(
                            children: [
                              // Offer Charges button
                              Expanded(
                                child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryColor,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: TextButton(
                                    onPressed: () {
                                      // Handle offer charges
                                    },
                                    child: const Text(
                                      'Offer Charges',
                                      style: TextStyle(
                                        color: AppColors.blackColor,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: FontsManager.urbanist,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              // Decline button
                              Expanded(
                                child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: AppColors.blackColor,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: TextButton(
                                    onPressed: () {
                                      // Handle decline
                                    },
                                    child: const Text(
                                      'Decline',
                                      style: TextStyle(
                                        color: AppColors.whiteColor,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: FontsManager.urbanist,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;

    // Animate camera and add markers only after the map controller is ready
    mapController
        .animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: _center, // Use the correct center
              zoom: 12.0,
            ),
          ),
        )
        .then((_) {
          _addMarkers(); // Call _addMarkers after the camera animation is complete
        });
  }

  void _addMarkers() {
    if (customMarkerIcon != null) {
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _center,
            infoWindow: const InfoWindow(
              title: 'Sahibzada Ajit Singh Nagar',
              snippet: 'My Custom Location',
            ),
            icon: customMarkerIcon!,
          ),
        );
        _markers.add(
          Marker(
            markerId: const MarkerId('another_place'),
            position: const LatLng(30.7333, 76.7794),
            infoWindow: const InfoWindow(
              title: 'Nearby City',
              snippet: 'Another custom marker',
            ),
            icon: customMarkerIcon!,
          ),
        );
      });
    }
  }

  Widget _buildLocationRow({
    required String label,
    required String location,
    required bool isPickup,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location dot
        Container(
          width: 8,
          height: 8,
          margin: const EdgeInsets.only(top: 6),
          decoration: BoxDecoration(
            color: isPickup ? Colors.green : Colors.red,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),

        // Location details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.lightGrayText,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                location,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontsManager.urbanist,
                  color: AppColors.blackColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem({required String icon, required String text}) {
    return Row(
      children: [
        Image.asset(icon, width: 16, height: 16),
        const SizedBox(width: 6),
        Text(
          text,
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            fontFamily: FontsManager.urbanist,
            color: AppColors.primaryGreyColor,
          ),
        ),
      ],
    );
  }
}
