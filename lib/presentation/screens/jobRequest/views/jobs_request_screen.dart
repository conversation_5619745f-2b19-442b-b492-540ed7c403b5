import 'package:go_router/go_router.dart';
import 'package:towchain_service_provider/core/widgets/CustomAppBar.dart';
import 'package:towchain_service_provider/core/widgets/custom_text.dart';
import 'package:towchain_service_provider/core/widgets/custom_button.dart';
import 'package:towchain_service_provider/presentation/screens/home/<USER>/Job_Request_Card.dart';
import 'package:towchain_service_provider/presentation/screens/jobRequest/model/JobRequestModel.dart';
import '../../../../core/imports/core_imports.dart';
import '../../../../core/assets_manager.dart';

class JobsRequestScreen extends StatefulWidget {
  const JobsRequestScreen({super.key});

  @override
  State<JobsRequestScreen> createState() => _JobsRequestScreenState();
}

class _JobsRequestScreenState extends State<JobsRequestScreen> {
  // Sample job requests data
  final List<JobRequest> jobRequests = [
    JobRequest(
      id: '1',
      customerName: '<PERSON>',
      customerImage: AssetsManager.person, // Using placeholder
      pickupLocation: 'Westheimer Rd Santa Ana, Illinois 85486',
      dropoffLocation: 'Thornridge Cir at 1901 Shiloh, Hawaii 81063',
      duration: '15min',
      distance: '2.5km',
      serviceType: 'Tire Repairs',
    ),
    JobRequest(
      id: '2',
      customerName: 'David Morel',
      customerImage: AssetsManager.person, // Using placeholder
      pickupLocation: 'Westheimer Rd Santa Ana, Illinois 85486',
      dropoffLocation: 'Thornridge Cir at 1901 Shiloh, Hawaii 81063',
      duration: '15min',
      distance: '2.5km',
      serviceType: 'Tire Repairs',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightestGreyColor,
      appBar: CustomAppBar(
        title: 'Active Job Requests',
        centerTitle: false,
        leading: IconButton(
          icon: Image.asset(AssetsManager.arrowLeft, width: 24, height: 24),
          onPressed: () {
            context.pop();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView.builder(
          itemCount: jobRequests.length,
          itemBuilder: (context, index) {
            final job = jobRequests[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: JobRequestCard(
                job: job,
                onDetailsPressed: () {
                  _showJobDetails(job);
                },
              ),
            );
          },
        ),
      ),
    );
  }

  void _showJobDetails(JobRequest job) {
    context.push('/jobsRequestDetail');
  }
}
