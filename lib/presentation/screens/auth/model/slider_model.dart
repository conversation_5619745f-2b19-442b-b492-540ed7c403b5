import 'package:towchain_service_provider/core/assets_manager.dart';

class SliderModel {
  final String image;
  final String title;
  final String description;

  // Constructor for variables
  SliderModel({
    required this.title,
    required this.description,
    required this.image,
  });
}

// List of onboarding slides
List<SliderModel> getSlides() {
  return [
    SliderModel(
      image: AssetsManager.on_board_one,
      title: "Accept a Job",
      description: "Lorem ipsum si amet, cinsecteur adipicing elit, sed do elusmod tempor Lorem ipsum si amet..",
    ),
    SliderModel(
      image: AssetsManager.on_board_two,
      title: "Tracking Real Time",
      description: "Lorem ipsum si amet, cinsecteur adipicing elit, sed do elusmod tempor Lorem ipsum si amet..",
    ),
    SliderModel(
      image: AssetsManager.on_board_three,
      title: "Earn Money",
      description: "Lorem ipsum si amet, cinsecteur adipicing elit, sed do elusmod tempor Lorem ipsum si amet..",
    ),
  ];
}