import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/utils/validator.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';
import '../widgets/build_header.dart';
import '../widgets/phone_form_field.dart';

class VerifyOtpScreen extends StatefulWidget {
  const VerifyOtpScreen({super.key});

  @override
  State<VerifyOtpScreen> createState() => _VerifyOtpScreenSate();
}

class _VerifyOtpScreenSate extends State<VerifyOtpScreen> {


  // Key for form validation (a good practice to add)
  final _formKey = GlobalKey<FormState>();

  final passwordController = TextEditingController();
  final fieldOne = TextEditingController();
  final fieldTwo = TextEditingController();
  final fieldThree = TextEditingController();
  final fieldFour = TextEditingController();

// Timer active state

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            CommonHeader(
              title: 'Verify Your Account',
              subtitle: "We’ve sent a 6-digit verification code to your\n [email/phone]. Please enter it below to continue.",
              clickableSubtitleText: "",
              backgroundImage: AssetsManager.authbg,
              onSubtitleClicked: () {
                // context.push('/signup');
              },
              // Replace with your image path
              onBackButtonPressed: () {
                context.pop();
              },
              height: 200.0,
            ),      // --- Form Section ---
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 34),
                     Image.asset(AssetsManager.otp,height: 120,width: 120,alignment: Alignment.center,),
                      Text(
                        'Enter the code sent to +91-XXXXXX1234',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 30),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _otpField(context, fieldOne),
                            const SizedBox(width: 5),
                            _otpField(context, fieldTwo),
                            const SizedBox(width: 5),
                            _otpField(context, fieldThree),
                            const SizedBox(width: 5),
                            _otpField(context, fieldFour),
                            const SizedBox(width: 5),

                          ],
                        ),
                      ),

                      // Checkbox and terms
                      const SizedBox(height: 32),
                      // Sign Up button
                      CustomButton(
                        height: 55,
                        fontSize: 16,
                        // Change button text based on the current page
                        text: "Confirm",
                        weight: FontWeight.w700,
                        textColor: AppColors.whiteColor,
                        color: AppColors.blackColor,
                        onPressed: () {
                          context.push('/login');

                        },

                      ),
                      const SizedBox(height: 20),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              context.push('/login');
                            },
                            child: Text(
                              'Didn’t receive the code? Resend',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            )

                          ),
                        ],
                      ),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }



  Widget _otpField(BuildContext context, TextEditingController controller) {
    return SizedBox(
        width: 50,
        height: 100,
        child: TextField(
          controller: controller,
          autofocus: true,
          onChanged: (value) {
            if (value.length == 1) {
              FocusScope.of(context).nextFocus();
            }
          },
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          cursorColor: AppColors.blackTextColor,
          maxLength: 1,
          style: TextStyle(
            color: AppColors.blackColor,
            fontSize: 14,
            fontFamily: "Urbanist-Regular",
            fontWeight: FontWeight.w400,
          ),
          decoration: InputDecoration(
            hintStyle: TextStyle(
              color: AppColors.hintColor,
              fontSize: 15,
              fontFamily: "Urbanist-Regular",
              fontWeight: FontWeight.w400,
            ),
            contentPadding: EdgeInsets.symmetric(
              vertical: 1 > 1 ? 15 : 15, // Adjust padding for multi-line
              horizontal: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                color: AppColors.primaryBorderColor,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                color: AppColors.primaryBorderColor,
                width: 1,
              ),
            ),
            isDense: true,
          ),
        ));
  }
}