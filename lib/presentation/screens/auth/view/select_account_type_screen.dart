import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/utils/validator.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';
import '../widgets/build_header.dart';
import '../widgets/phone_form_field.dart';

class SelectAccountTypeScreen extends StatefulWidget {
  const SelectAccountTypeScreen({super.key});

  @override
  State<SelectAccountTypeScreen> createState() => _SelectAccountTypeScreenState();
}

class _SelectAccountTypeScreenState extends State<SelectAccountTypeScreen> {

  bool _agreeToTerms = false;

  // Key for form validation (a good practice to add)
  final _formKey = GlobalKey<FormState>();

  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();

  final passwordController = TextEditingController();

  String _selectedAccountType = 'business'; // default to 'individual'


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [

            // --- Header Section ---
            CommonHeader(
              title: 'Create Your Account',
              subtitle: "Already have an account?",
              clickableSubtitleText: "Login",
              backgroundImage: AssetsManager.authbg,
              onSubtitleClicked: () {
                context.go('/login');
              },
              // Replace with your image path
              onBackButtonPressed: () {
                Navigator.of(context).pop();
              },
            ), // --- Form Section ---
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 24),
                      Text(
                        "Choose Your Account Type",
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          fontFamily: 'Urbanist',
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Individual Button
                      CustomButton(
                        height: 55,
                        fontSize: 16,
                        text: "Individual",
                        weight: FontWeight.w700,
                        textColor: _selectedAccountType == 'individual'
                            ? AppColors.whiteColor // Highlighted color
                            : AppColors.blackColor, // Default color
                        color: _selectedAccountType == 'individual'
                            ? AppColors.primaryColor // Highlighted color
                            : AppColors.lightestGreyColor, // Default color
                        onPressed: () {
                          setState(() {
                            _selectedAccountType = 'individual';
                          });
                        },
                      ),
                      const SizedBox(height: 24),

// Business Button
                      CustomButton(
                        height: 55,
                        fontSize: 16,
                        text: "Business",
                        weight: FontWeight.w700,
                        textColor: _selectedAccountType == 'business'
                            ? AppColors.whiteColor // Highlighted color
                            : AppColors.blackColor, // Default color
                        color: _selectedAccountType == 'business'
                            ? AppColors.primaryColor // Highlighted color
                            : AppColors.lightestGreyColor, // Default color
                        onPressed: () {
                          setState(() {
                            _selectedAccountType = 'business';
                          });
                        },
                      ),
                      const SizedBox(height: 350),

                      CustomButton(
                        height: 55,
                        fontSize: 16,
                        // Change button text based on the current page
                        text: "Next",
                        weight: FontWeight.w700,
                        textColor: AppColors.primaryColor,
                        color: AppColors.blackColor,
                        onPressed: () {
                          if(_selectedAccountType=='business')
                            {
                              context.go('/businessSignup');
                            }
                          else{
                            context.go('/signup');

                          }


                        },

                      ),
                      const SizedBox(height: 40),

                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }





}
