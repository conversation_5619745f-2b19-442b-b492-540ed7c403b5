import 'package:flutter/material.dart';
import 'package:country_picker/country_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

import '../../../../core/theme/colors.dart';
import '../../../../core/widgets/custom_text.dart';

// Assuming CustomText, AppColors are defined elsewhere
class PhoneForm<PERSON>ield extends StatefulWidget {
  const PhoneFormField({Key? key}) : super(key: key);

  @override
  State<PhoneFormField> createState() => _PhoneFormFieldState();
}

class _PhoneFormFieldState extends State<PhoneFormField> {
  // Store the selected country code
  Country _selectedCountry = Country(
    phoneCode: '1',
    countryCode: 'US',
    e164Sc: 0,
    geographic: true,
    level: 1,
    name: 'United States',
    example: '2012345678',
    displayName: 'United States (US)',
    displayNameNoCountryCode: 'United States',
    e164Key: '',
  );

  @override
  void initState() {
    super.initState();
    // Fetch the user's country code automatically when the widget initializes
    _fetchCurrentCountry();
  }

  // --- Start of new methods ---

  Future<void> _fetchCurrentCountry() async {
    try {
      Position position = await _determinePosition();
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        String? countryCode = placemarks.first.isoCountryCode;
        if (countryCode != null) {
          Country country = Country.tryParse(countryCode) ?? _selectedCountry;
          setState(() {
            _selectedCountry = country;
          });
        }
      }
    } catch (e) {
      // Handle errors, e.g., location permissions denied
      print("Could not get location: $e");
    }
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
        'Location permissions are permanently denied, we cannot request permissions.',
      );
    }

    return await Geolocator.getCurrentPosition();
  }

  void _openCountryPicker() {
    showCountryPicker(
      context: context,
      showPhoneCode: true,
      onSelect: (Country country) {
        setState(() {
          _selectedCountry = country;
        });
      },
    );
  }

  // --- End of new methods ---

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: "Phone Number",
          color: AppColors.blackColor,
          size: 14,
          weight: FontWeight.w500,
        ),
        const SizedBox(height: 10),
        TextFormField(
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            hintText: "Enter your phone number",
            hintStyle: const TextStyle(
              color: AppColors.hintColor,
              fontSize: 14,
              fontFamily: "Urbanist",
              fontWeight: FontWeight.w400,
            ),
            // ... (rest of your InputDecoration properties)
            isDense: true,
            prefixIcon: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: GestureDetector(
                onTap: _openCountryPicker, // Make the prefix icon tappable
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Display the flag of the selected country
                    Text(
                      _selectedCountry.flagEmoji,
                      style: const TextStyle(fontSize: 20),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '+${_selectedCountry.phoneCode}',
                      // Display the phone code
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const SizedBox(
                      height: 24,
                      child: VerticalDivider(
                        color: AppColors.lightestGreyColor,
                        thickness: 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                color: AppColors.primaryBorderColor,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: AppColors.lightestGreyColor,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                color: AppColors.primaryBorderColor,
                width: 1,
              ),
            ),
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }
}

// NOTE: You'll need to create a simple CustomText and AppColors class
// or replace them with standard Flutter widgets and colors.
