import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/assets_manager.dart';

class CommonHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final String backgroundImage;
  final VoidCallback? onBackButtonPressed;
  final double height;
  final String? clickableSubtitleText;
  final VoidCallback? onSubtitleClicked;

  const CommonHeader({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.backgroundImage,
    this.onBackButtonPressed,
    this.height = 190.0,
    this.clickableSubtitleText,
    this.onSubtitleClicked,
  }) : super(key: key);

  // A reusable text style for subtitles
  static const TextStyle _subtitleStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: Colors.black,
    fontFamily: 'Urbanist',
  );

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: Stack(
        children: <Widget>[
          // Background Image
          Image.asset(
            AssetsManager.authbg,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),

          // Optional back button at the top left
          if (onBackButtonPressed != null)
            SafeArea(
              child: Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: const EdgeInsets.only(left: 12, top: 0),
                  child: IconButton(
                    padding: EdgeInsets.zero, // Remove default padding
                    alignment: Alignment.topLeft,
                    icon: SvgPicture.asset(
                      AssetsManager.back,
                      height: 40,
                      width: 40,
                    ),
                    onPressed: onBackButtonPressed,
                  ),
                ),
              ),
            ),

          // All content, aligned to the bottom-left
          Align(
            alignment: Alignment.bottomLeft,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Title
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 25,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                      fontFamily: 'Urbanist',
                    ),
                  ),
                  const SizedBox(height: 4),

                  // Subtitle (with an optional clickable part)
                  Row(
                    children: <Widget>[
                      // The main part of the subtitle
                      Text(
                        subtitle,
                        style: _subtitleStyle,
                        maxLines: 2,
                      ),

                      // Clickable part
                      if (clickableSubtitleText != null &&
                          onSubtitleClicked != null) ...[
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: onSubtitleClicked,
                          child: Text(
                            clickableSubtitleText!,
                            style: _subtitleStyle.copyWith(
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 16), // Add bottom padding
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
